// Test local para la función Lambda
const { handler } = require('./lambda-handler');
const fs = require('fs');

async function testLambda() {
  console.log('Probando función Lambda localmente...');

  // Evento de prueba
  const testEvent = {
    "planillaData": {
      "codigo": "25897",
      "version": "01.05",
      "fecha": "2025-07-09 10:25:15",
      "consecutivo": "0009",
      "mensajero": "Alexandra Yo<PERSON> Valencia Florez",
      "registros": [
        {
          "fechaRadicado": "09/07/25\n10:39:15",
          "numeroRadicado": "20250709001",
          "asunto": "Prueba Lambda AWS",
          "entidad": "Empresa Test",
          "remitente": "Departamento IT",
          "seccion": "Gerencia",
          "destinatario": "<PERSON>\nV<PERSON><PERSON>",
          "folios": "3",
          "cc": "",
          "pasaA": "",
          "firma": ""
        },
        {
          "fechaRadicado": "09/07/25\n11:15:30",
          "numeroRadicado": "20250709002",
          "asunto": "Documento de seguimiento",
          "entidad": "Proveedor ABC",
          "remitente": "Juan Pérez",
          "seccion": "Compras",
          "destinatario": "Ana García\nSupervisora",
          "folios": "1",
          "cc": "X",
          "pasaA": "Archivo",
          "firma": ""
        }
      ]
    },
    "uploadToS3": false
  };



  try {
    const result = await handler(testEvent);

    if (result.statusCode === 200 && result.isBase64Encoded) {
      // Guardar PDF localmente para verificar
      const pdfBuffer = Buffer.from(result.body, 'base64');
      fs.writeFileSync('test-output.pdf', pdfBuffer);
      console.log('✅ PDF generado exitosamente: test-output.pdf');
    } else {
      console.log('📄 Respuesta:', result);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testLambda();
